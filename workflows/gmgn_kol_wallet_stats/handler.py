"""
GMGN KOL钱包统计数据工作流处理器

本模块实现了GMGN KOL钱包统计数据的工作流处理逻辑，包括：
- 生成待更新的钱包地址列表
- 处理单个钱包的统计数据获取
- 批量存储钱包统计数据
- 数据验证和错误处理
"""

import asyncio
import logging
import traceback
from typing import Any, Dict, List, Optional
from datetime import datetime

from dao.gmgn_wallet_stats_dao import GmgnWalletStatsDAO
from utils.spiders.smart_money.gmgn_wallet_stats_spider import GmgnWalletStatsSpider
from models.gmgn_wallet_stats import GmgnWalletStats


logger = logging.getLogger("GmgnKolWalletStatsHandler")
gmgn_wallet_stats_dao = GmgnWalletStatsDAO()


async def generate_wallet_addresses() -> List[str]:
    """
    生成待更新的钱包地址列表
    
    从kol_wallets表中获取需要更新统计数据的钱包地址。
    基于最后更新时间进行筛选，优先处理长时间未更新的钱包。
    
    Returns:
        List[str]: 钱包地址列表
    """
    try:
        # 获取需要更新的钱包地址，限制50个，超过1小时未更新的优先
        wallet_addresses = await gmgn_wallet_stats_dao.find_wallets_need_stats_update(
            limit=50,
            hours_threshold=1,
            period="all"  # 默认获取全时间统计数据
        )
        
        logger.info(f"生成了 {len(wallet_addresses)} 个待更新的钱包地址")
        
        if not wallet_addresses:
            logger.info("当前没有需要更新的钱包地址")
            return []
        
        # 记录要处理的钱包地址
        logger.info(f"待处理钱包地址列表: {wallet_addresses[:10]}{'...' if len(wallet_addresses) > 10 else ''}")
        
        return wallet_addresses
        
    except Exception as e:
        error_msg = f"生成钱包地址列表时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return []


async def process_wallet_stats(wallet_address: str) -> Optional[Dict]:
    """
    处理单个钱包的统计数据获取
    
    Args:
        wallet_address: 钱包地址
        
    Returns:
        Optional[Dict]: 处理后的钱包统计数据，如果处理失败则返回None
    """
    if not wallet_address:
        logger.warning("收到空的钱包地址")
        return None
    
    logger.info(f"开始处理钱包统计数据: {wallet_address}")
    
    try:
        # 初始化爬虫
        spider = GmgnWalletStatsSpider(max_retries=3, retry_interval=2.0)
        
        # 获取钱包统计数据
        stats = await spider.get_wallet_stats(wallet_address, period="all")
        
        if not stats:
            logger.warning(f"无法获取钱包 {wallet_address} 的统计数据")
            return None
        
        # 转换为字典格式用于传递
        stats_dict = {
            "wallet_address": wallet_address,
            "period": "all",
            "chain": "sol",
            "buy": stats.buy,
            "sell": stats.sell,
            "trade_frequency_total": stats.trade_frequency_total,  # 使用正确的属性名
            "winrate": stats.winrate,  # 使用正确的属性名
            "pnl": stats.pnl,  # 使用正确的属性名
            "pnl_7d": stats.pnl_7d,  # 使用正确的属性名
            "unrealized_pnl": stats.unrealized_pnl,
            "total_profit": stats.total_profit,
            "avg_cost": stats.avg_cost,  # 使用正确的属性名
            "avg_sold": stats.avg_sold,  # 使用正确的属性名
            "last_active_timestamp": stats.last_active_timestamp,  # 使用正确的属性名
            "risk": {
                "token_active": stats.risk.token_active if stats.risk else None,
                "token_honeypot": stats.risk.token_honeypot if stats.risk else None,
                "no_buy_hold": stats.risk.no_buy_hold if stats.risk else None,
                "fast_tx": stats.risk.fast_tx if stats.risk else None,
            } if stats.risk else None,
            "data_timestamp": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        logger.info(f"成功获取钱包 {wallet_address} 的统计数据")
        return stats_dict
        
    except Exception as e:
        error_msg = f"处理钱包 {wallet_address} 统计数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return None
    finally:
        # 清理爬虫资源
        try:
            if 'spider' in locals():
                await spider.close()
        except Exception as cleanup_error:
            logger.warning(f"清理爬虫资源时出错: {cleanup_error}")


async def store_wallet_stats(data: List[Dict]) -> int:
    """
    批量存储钱包统计数据
    
    Args:
        data: 钱包统计数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        logger.info("没有数据需要存储")
        return 0
    
    try:
        # 确保数据是列表格式
        stats_list = data if isinstance(data, list) else [data]
        
        # 过滤掉无效数据
        valid_stats = []
        for stats_dict in stats_list:
            if validate_wallet_data(stats_dict):
                valid_stats.append(stats_dict)
            else:
                logger.warning(f"跳过无效的钱包数据: {stats_dict.get('wallet_address', 'unknown')}")
        
        if not valid_stats:
            logger.warning("所有数据都无效，无法存储")
            return 0
        
        # 批量存储到数据库
        result = await gmgn_wallet_stats_dao.batch_upsert_wallet_stats(
            valid_stats, 
            period="all"
        )
        
        success_count = result.get("success_count", 0)
        error_count = result.get("error_count", 0)
        
        logger.info(f"批量存储完成: 成功 {success_count} 条，失败 {error_count} 条")
        
        # 如果有错误，记录详细信息
        if error_count > 0:
            errors = result.get("errors", [])
            for error in errors[:5]:  # 只记录前5个错误
                logger.error(f"存储错误: {error}")
        
        return success_count
        
    except Exception as e:
        error_msg = f"批量存储钱包统计数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return 0


def validate_wallet_data(data: Any) -> bool:
    """
    验证钱包数据的有效性
    
    Args:
        data: 钱包数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    try:
        # 检查数据类型
        if not isinstance(data, dict):
            logger.warning("钱包数据不是字典格式")
            return False
        
        # 检查必需字段
        required_fields = ["wallet_address", "period", "chain"]
        for field in required_fields:
            if field not in data or not data[field]:
                logger.warning(f"钱包数据缺少必需字段: {field}")
                return False
        
        # 验证钱包地址格式
        wallet_address = data["wallet_address"]
        if not isinstance(wallet_address, str) or len(wallet_address) < 10:
            logger.warning(f"钱包地址格式无效: {wallet_address}")
            return False
        
        # 验证时间窗口
        period = data["period"]
        if period not in ["all", "7d", "1d"]:
            logger.warning(f"无效的时间窗口: {period}")
            return False
        
        # 验证链名称
        chain = data["chain"]
        if chain != "sol":
            logger.warning(f"不支持的链: {chain}")
            return False
        
        # 验证数值字段（如果存在）
        numeric_fields = ["buy", "sell", "total_count", "win_rate", "realized_pnl"]
        for field in numeric_fields:
            if field in data and data[field] is not None:
                value = data[field]
                if not isinstance(value, (int, float, str)):
                    logger.warning(f"数值字段 {field} 类型无效: {type(value)}")
                    return False
                
                # 尝试转换为数值
                try:
                    if isinstance(value, str):
                        float(value)
                except (ValueError, TypeError):
                    logger.warning(f"数值字段 {field} 无法转换为数字: {value}")
                    return False
        
        logger.debug(f"钱包数据验证通过: {wallet_address}")
        return True
        
    except Exception as e:
        logger.error(f"验证钱包数据时出错: {e}")
        return False


async def get_wallet_stats_multiple_periods(wallet_address: str) -> Optional[Dict[str, Dict]]:
    """
    获取钱包多个时间窗口的统计数据
    
    Args:
        wallet_address: 钱包地址
        
    Returns:
        Optional[Dict[str, Dict]]: 时间窗口到统计数据的映射，失败时返回None
    """
    if not wallet_address:
        logger.warning("收到空的钱包地址")
        return None
    
    try:
        spider = GmgnWalletStatsSpider(max_retries=3, retry_interval=1.0)
        
        # 获取多个时间窗口的数据
        periods_stats = await spider.get_multiple_periods_stats(
            wallet_address, 
            periods=["all", "7d", "1d"]
        )
        
        if not periods_stats:
            logger.warning(f"无法获取钱包 {wallet_address} 的多时间窗口统计数据")
            return None
        
        # 转换为字典格式
        result = {}
        for period, stats in periods_stats.items():
            if stats:
                result[period] = {
                    "wallet_address": wallet_address,
                    "period": period,
                    "chain": "sol",
                    "buy": stats.buy,
                    "sell": stats.sell,
                    "total_count": stats.total_count,
                    "win_rate": stats.win_rate,
                    "realized_pnl": stats.realized_pnl,
                    "data_timestamp": datetime.utcnow()
                }
            else:
                result[period] = None
        
        logger.info(f"成功获取钱包 {wallet_address} 的多时间窗口统计数据")
        return result
        
    except Exception as e:
        error_msg = f"获取钱包 {wallet_address} 多时间窗口数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return None
    finally:
        try:
            if 'spider' in locals():
                await spider.close()
        except Exception:
            pass 